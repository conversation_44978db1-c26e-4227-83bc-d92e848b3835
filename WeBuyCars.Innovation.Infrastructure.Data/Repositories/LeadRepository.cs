using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Innovation.Core.Entities;
using WeBuyCars.Innovation.Core.SharedKernel;
using WeBuyCars.Innovation.Infrastructure.Data.SQL.Context;

namespace WeBuyCars.Innovation.Infrastructure.Data.SQL.Repositories;

public class LeadRepository(InnovationContext context) : Repository<Lead>(context), ILeadRepository
{
    public async Task<DynamicLinqResult> GetAllLeadsAsync(DynamicLinqRequest request)
    {
        return await _context.Lead.ToDynamicLinqResultAsync(request);
    }
    
    public Task<Lead> FirstOrDefaultAsync(Expression<Func<Lead, bool>> predicate)
    {
        return _context.Lead.FirstOrDefaultAsync(predicate);
    }
}