using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Innovation.Core.Entities;

namespace WeBuyCars.Innovation.Infrastructure.Data.SQL.EntityConfigurations;

public class LeadEntityConfiguration : IEntityTypeConfiguration<Lead>
{
    public void Configure(EntityTypeBuilder<Lead> builder)
    {
        builder.HasKey(l => l.Id);

        builder.Property(l => l.LeadNumber)
            .HasMaxLength(LeadConstants.MaxLeadNumberLength)
            .IsRequired();

        builder.Property(l => l.LeadReference)
            .HasMaxLength(LeadConstants.MaxLeadReferenceLength);

        builder.Property(l => l.BatchReference)
            .HasMaxLength(LeadConstants.MaxBatchReferenceLength);

        builder.Property(l => l.ForeignReference)
            .HasMaxLength(LeadConstants.MaxForeignReferenceLength);

        builder.Property(l => l.IGProductType)
            .HasMaxLength(LeadConstants.MaxIGProductTypeLength)
            .IsRequired();

        builder.Property(l => l.IGEventType)
            .HasMaxLength(LeadConstants.MaxIGEventTypeLength)
            .IsRequired(false);

        builder.Property(l => l.IGSuccess)
            .IsRequired(false);

        builder.Property(l => l.IGSuccessDate);

        builder.Property(l => l.IGCreated)
            .IsRequired(false);

        builder.Property(l => l.IGCreatedOn)
            .IsRequired();

        builder.Property(l => l.IGCreatedId)
            .HasMaxLength(LeadConstants.MaxIGCreatedIdLength);

        builder.Property(l => l.IGUpdated)
            .IsRequired(false);

        builder.Property(l => l.IGUpdateOn);

        builder.Property(l => l.IGUpdateId)
            .HasMaxLength(LeadConstants.MaxIGUpdateIdLength);

        builder.Property(l => l.IGRejected)
            .IsRequired(false);

        builder.Property(l => l.IGRejectedOn);

        builder.Property(l => l.IGRejectedId)
            .HasMaxLength(LeadConstants.MaxIGRejectedIdLength);

        builder.Property(l => l.IGCancelled)
            .IsRequired(false);

        builder.Property(l => l.IGCancelledOn);

        builder.Property(l => l.IGCancelledId)
            .HasMaxLength(LeadConstants.MaxIGCancelledIdLength);
            
        builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
    }
}