using System;
using System.Collections.Generic;

namespace WeBuyCars.Innovation.Infrastructure.Innovation.Models.Leads;

public sealed class InnovationLeadPayload
{ 
    public string foreignReference { get; set; }
    public string leadDate { get; set; } = string.Empty;
    public List<string> products { get; set; }
    public List<string> productTypes { get; set; }
    public string leadSource { get; set; } = string.Empty;
    public bool consentGiven { get; set; } = true;
    public InnovationLeadEntity entity { get; set; }
    public List<InnovationLeadAddress> addresses { get; set; }
    public List<InnovationLeadContactDetail> contactDetails { get; set; }
    public List<InnovationLeadRiskItem> riskItems { get; set; }
    public InnovationLeadSalesOrganisation salesOrganisation { get; set; }
    public InnovationLeadSalesRepresentative salesRepresentative { get; set; }
    public string salesDepartment { get; set; } = string.Empty;
    public string leadType { get; set; } = string.Empty;
}
