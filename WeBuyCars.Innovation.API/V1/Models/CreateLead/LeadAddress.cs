using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WeBuyCars.Innovation.Api.V1.Models.CreateLead;

public class LeadAddress
{
    [JsonIgnore]
    public string ForeignReference { get; set; }
    [Required]
    public required string Address1 { get; set; }
    public string Address2 { get; set; } = string.Empty;
    public string Address3 { get; set; } = string.Empty;
    public string Address4 { get; set; } = string.Empty;
    [Required]
    public required string AddressCode { get; set; }
    [Required]
    public required string AddressType { get; set; } // postal, physical
}