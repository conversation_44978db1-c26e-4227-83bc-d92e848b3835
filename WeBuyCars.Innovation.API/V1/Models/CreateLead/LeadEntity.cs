using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WeBuyCars.Innovation.Api.V1.Models.CreateLead;

public class LeadEntity
{
    [Required]
    public required string EntityType { get; set; } // Natural, Juristic
    public string Title { get; set; } = string.Empty;
    public string Initials { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; }
    public string IdentityNumber { get; set; } = string.Empty;
    public string PassportNumber { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public string AaMembershipNumber { get; set; } = string.Empty;
    public string CommunicationMethod { get; set; } = string.Empty;
    public string Relation { get; set; } = string.Empty;
    public string CountryOfBirth { get; set; } = string.Empty;
    public string CountryOfResidence { get; set; } = string.Empty;
    public string CountryPassportIssued { get; set; } = string.Empty;
    [JsonIgnore]
    public string ForeignReference { get; set; }
    public string AmlCftReference { get; set; } = string.Empty;
}