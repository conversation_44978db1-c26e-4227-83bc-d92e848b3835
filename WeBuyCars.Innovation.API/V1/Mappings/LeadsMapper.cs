using System.Collections.Generic;
using AutoMapper;
using Innovation.Contracts;
using WeBuyCars.Innovation.Api.V1.Models.Callback;
using WeBuyCars.Innovation.Api.V1.Models.CreateLead;
using WeBuyCars.Innovation.Infrastructure.Innovation.Models.Leads;

namespace WeBuyCars.Innovation.Api.V1.Mappings;

public class LeadsMapper : Profile
{
    public LeadsMapper()
    {
        // Create Lead
        CreateMap<CreateLeadRequest, InnovationCreateLeadMessage>()
            .ForMember(dest => dest.batchReference, opt => opt.MapFrom(src => src.BatchReference))
            .ForMember(dest => dest.payload, opt => opt.MapFrom(src => src.Payload));
        
        CreateMap<InnovationCreateLeadMessage, CreateLeadRequest>()
            .ForMember(dest => dest.BatchReference, opt => opt.MapFrom(src => src.batchReference))
            .ForMember(dest => dest.Payload, opt => opt.MapFrom(src => src.payload));

        CreateMap<LeadPayloadMessage, LeadPayload>().ReverseMap();
        CreateMap<LeadEntity, LeadEntityMessage>().ReverseMap();
        CreateMap<LeadAddress, LeadAddressMessage>().ReverseMap();
        CreateMap<LeadContactDetail, LeadContactDetailMessage>().ReverseMap();
        CreateMap<LeadRiskItem, LeadRiskItemMessage>().ReverseMap();
        CreateMap<LeadSalesOrganisation, LeadSalesOrganisationMessage>().ReverseMap();
        CreateMap<LeadSalesRepresentative, LeadSalesRepresentativeMessage>().ReverseMap();
        
        // Innovation Lead Create
        CreateMap<CreateLeadRequest, InnovationLeadBatchMessageRequest>()
            .ForMember(dest => dest.batchReference, opt => opt.MapFrom(src => src.BatchReference))
            .ForMember(dest => dest.payload, opt => opt.MapFrom(src => src.Payload));

        CreateMap<LeadPayload, InnovationLeadPayload>()
            .ForMember(dest => dest.leadDate, opt => opt.MapFrom(src => src.LeadDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ")))
            ;
        
        CreateMap<LeadEntity, InnovationLeadEntity>();
        CreateMap<LeadAddress, InnovationLeadAddress>();
        CreateMap<LeadContactDetail, InnovationLeadContactDetail>();
        CreateMap<LeadRiskItem, InnovationLeadRiskItem>()
            .ForMember(dest => dest.registrationDate, opt => opt.MapFrom(src => src.RegistrationDate.ToString("yyyy-MM-ddTHH:mm:ss.fffffffK")))
            ;
        CreateMap<LeadSalesOrganisation, InnovationLeadSalesOrganisation>();
        CreateMap<LeadSalesRepresentative, InnovationLeadSalesRepresentative>();

        // Create Lead Callback
        CreateMap<LeadCallbackResponse, InnovationLeadCallbackMessage>().ReverseMap();
        CreateMap<LeadCallbackResponseReason, InnovationLeadCallbackReasonsMessage>().ReverseMap();
        CreateMap<InnovationLeadCallbackBatchMessage, List<LeadCallbackResponse>>()
            .ConvertUsing((src, dest, context) => context.Mapper.Map<List<LeadCallbackResponse>>(src.Items));
    }
}