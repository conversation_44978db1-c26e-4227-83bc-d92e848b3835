using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Asp.Versioning;
using AutoMapper;
using Innovation.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Innovation.Api.V1.Models.Callback;
using WeBuyCars.Innovation.Api.V1.Models.CreateLead;
using WeBuyCars.Innovation.Core.Constants;
using WeBuyCars.Innovation.Core.SharedKernel;
using WeBuyCars.Innovation.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Innovation.Api.V1.Controllers;

[ApiController]
[Route("v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class LeadController(IMessageService messagingService, ILeadRepository leadRepository, I<PERSON>apper mapper, ILogger<LeadController> logger) : ControllerBase
{
    [HttpPost("Create")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [Authorize("CanCreate")]
    [AllowAnonymous]
    public async Task<IActionResult> CreateLeadBatch([FromBody] CreateLeadRequest request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Create Lead Request: {Request}", request);
        
        try
        {
            request.BatchReference = Guid.NewGuid();
            
            foreach (var lead in request.Payload)
            {
                AssignForeignReference(lead);
            }
            
            var message = mapper.Map<InnovationCreateLeadMessage>(request);
            await  messagingService.SendMessageToQueue(message, MasstransitConstants.InnovationCreateLead, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unable to create an Innovation lead for payload: {Payload}.", JsonConvert.SerializeObject(request.Payload, Formatting.Indented));
            throw;
        }

        return Created(string.Empty, new CreateLeadResponse
        {
            ReferenceNumber = request.BatchReference
        });
    }

    /// <summary>
    /// Innovation Group Lead Callback Responses
    /// </summary>
    /// <param name="callbackResponse"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("Callback")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesDefaultResponseType]
    [AllowAnonymous]
    public async Task<IActionResult> InnovationCallback([FromBody] List<LeadCallbackResponse> callbackResponse, CancellationToken cancellationToken)
    {
        logger.LogInformation("Innovation Lead Callback Response: {Response}",
            JsonConvert.SerializeObject(callbackResponse, Formatting.Indented));

        if (callbackResponse == null || callbackResponse.Count == 0)
            return BadRequest();
        
        var message = new InnovationLeadCallbackBatchMessage
        {
            Items = mapper.Map<List<InnovationLeadCallbackMessage>>(callbackResponse)
        };
        await messagingService.SendMessageToQueue(message, MasstransitConstants.InnovationLeadCallback, cancellationToken);
        return Accepted();
    }

    /// <summary>
    /// Get Leads
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("GetLeads")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetLeads([FromBody] DynamicLinqRequest request)
    {
        var result = await leadRepository.GetAllLeadsAsync(request);
        return Ok(result);
    }
    
    #region Private Methods
    
    private static void AssignForeignReference(LeadPayload lead)
    {
        lead.ForeignReference = Guid.NewGuid().ToString();

        if (lead.Addresses != null)
        {
            foreach (var address in lead.Addresses.Where(address => address != null))
            {
                address.ForeignReference = lead.ForeignReference;
            }
        }

        if (lead.ContactDetails != null)
        {
            foreach (var contact in lead.ContactDetails.Where(contact => contact != null))
            {
                contact.ForeignReference = lead.ForeignReference;
            }
        }

        if (lead.RiskItems != null)
        {
            foreach (var risk in lead.RiskItems.Where(risk => risk != null))
            {
                risk.ForeignReference = lead.ForeignReference;
            }
        }

        if (lead.Entity != null)
            lead.Entity.ForeignReference = lead.ForeignReference;

        if (lead.SalesOrganisation != null)
            lead.SalesOrganisation.ForeignReference = lead.ForeignReference;

        if (lead.SalesRepresentative != null)
            lead.SalesRepresentative.ForeignReference = lead.ForeignReference;
    }
    
    #endregion
}