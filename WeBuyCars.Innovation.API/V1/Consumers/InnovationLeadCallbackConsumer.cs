using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Innovation.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using WeBuyCars.Innovation.Api.V1.Models.Callback;
using WeBuyCars.Innovation.Api.V1.Services.Interfaces;

namespace WeBuyCars.Innovation.Api.V1.Consumers;

public class InnovationLeadCallbackConsumer(ILeadsService leadsService, IMapper mapper, ILogger<InnovationLeadCallbackConsumer> logger) : IConsumer<InnovationLeadCallbackBatchMessage>
{
    public async Task Consume(ConsumeContext<InnovationLeadCallbackBatchMessage> context)
    {
        var message = context.Message;
        if(message.Items.Count == 0)
        {
            logger.LogInformation("Innovation Lead Callback Message is empty {Message}", message);
            return;
        }
        
        logger.LogInformation("Consuming Innovation Lead Callback Message: {Message}", message);
        var callbackResponse = mapper.Map<List<LeadCallbackResponse>>(message);
        await leadsService.ProcessCallbackEvent(callbackResponse);
    }
}