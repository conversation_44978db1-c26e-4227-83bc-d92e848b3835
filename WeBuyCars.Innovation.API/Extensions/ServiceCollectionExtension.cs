using System;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication.Extensions;
using WeBuyCars.Core.Infrastructure.Errors.Middleware;
using WeBuyCars.Core.Infrastructure.Scalar.Extensions;
using WeBuyCars.Core.Infrastructure.Swagger.Extensions;
using WeBuyCars.Innovation.Api.V1.Services;
using WeBuyCars.Innovation.Api.V1.Services.Interfaces;
using WeBuyCars.Innovation.Core.SharedKernel;
using WeBuyCars.Innovation.Infrastructure.Data.SQL.Context;
using WeBuyCars.Innovation.Infrastructure.Data.SQL.Repositories;
using WeBuyCars.Innovation.Infrastructure.Innovation.Extensions;
using WeBuyCars.Innovation.Infrastructure.Messaging.Services;
using WeBuyCars.Innovation.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Innovation.Api.Extensions;

public static class ServiceCollectionExtension
{
    public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        ConfigureMvc(services);
        ConfigureDbContext(services, configuration);
        ConfigureAuthentication(services, configuration);
        ConfigureAuthorization(services, configuration);
        ConfigureHealthChecks(services);
        ConfigureAutoMapper(services);
        ConfigureServices(services);
        ConfigureRepositories(services);
        ConfigureInnovationIntegration(services, configuration);
        ConfigureMassTransit(services, configuration);

        services.AddSwaggerDocumentation()
            .AddResponseCompression()
            .AddResponseCaching()
            .AddHttpContextAccessor()
            .AddAllElasticApm()
            .AddScalarDocumentation()
            .AddOptions();
    }

    public static void Configure(IApplicationBuilder app)
    {
        app.UseMiddleware<ExceptionMiddleware>();
        app.UseHttpsRedirection();
        app.UseSwaggerDocumentation();
        app.UseRouting();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseScalarDocumentation();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapHealthChecks("/health", new HealthCheckOptions
            {
                AllowCachingResponses = false
            });
        });
    }

    private static void ConfigureMvc(IServiceCollection services)
    {
        services.AddMvc(options => { options.EnableEndpointRouting = false; })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.JsonSerializerOptions.WriteIndented = true;
            })
            .AddNewtonsoftJson(o =>
            {
                o.SerializerSettings.Formatting = Formatting.Indented;
                o.SerializerSettings.Converters.Add(new StringEnumConverter());
                o.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                o.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                o.SerializerSettings.DateParseHandling = DateParseHandling.DateTimeOffset;
            });

        services.AddMvcCore().AddApiExplorer();
    }

    private static void ConfigureHealthChecks(IServiceCollection services)
    {
        var hcBuilder = services.AddHealthChecks();

        hcBuilder.AddCheck("self", () => HealthCheckResult.Healthy());
        hcBuilder.AddDbContextCheck<InnovationContext>();
    }

    private static void ConfigureAuthentication(IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthenticationSchemes(configuration);
        Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = false;
    }

    private static void ConfigureAuthorization(IServiceCollection services, IConfiguration configuration)
    {
        services.AddControllers(config =>
        {
            var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
            config.Filters.Add(new AuthorizeFilter(policy));
        });

        services.AddAuthorization(options =>
        {
            options.ConfigureAuthorization(configuration);

            var policies = new (string Name, string[] Claims)[]
            {
                // Administrators can perform all actions.
                ("IsAdmin", ["innovation_admin"]),
                // Creators can only perform create/post actions.
                ("CanCreate", ["innovation_admin", "innovation_create"]),
                // Readers can only perform read/get actions.
                ("CanRead", ["innovation_admin", "innovation_read"])
            };

            foreach (var (name, claims) in policies)
            {
                options.AddPolicy(name, policy =>
                    policy.RequireAssertion(context =>
                        claims.Any(claim => context.User.HasClaim(c => c.Type == claim || c.Value == claim))
                    )
                );
            }
        });
    }

    private static void ConfigureDbContext(IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<InnovationContext>((serviceProvider, options) =>
        {
            options.UseSqlServer(configuration.GetConnectionString("WBCDB01") + "Application Name=Innovation-api;",
                sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly("WeBuyCars.Innovation.Infrastructure.Data");
                    sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", InnovationContext.DEFAULT_SCHEMA);
                });
            options.ConfigureWarnings(warnings => warnings.Ignore(RelationalEventId.PendingModelChangesWarning));
        });
    }

    private static void ConfigureAutoMapper(IServiceCollection services)
    {
        services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        services.AddScoped<ILeadsService, LeadsService>();
        services.AddScoped<IMessageService, MessageService>();
    }

    private static void ConfigureRepositories(IServiceCollection services)
    {
        services.AddScoped<ILeadRepository, LeadRepository>();
    }

    private static void ConfigureInnovationIntegration(IServiceCollection services, IConfiguration configuration)
    {
        services.AddInnovationIntegrationService(configuration);
    }

    private static void ConfigureMassTransit(IServiceCollection services, IConfiguration configuration)
    {
        var currentEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        services.AddMassTransit(busConfigurator =>
        {
            busConfigurator.SetKebabCaseEndpointNameFormatter();
            busConfigurator.AddServiceBusMessageScheduler();

            busConfigurator.AddConsumers(Assembly.GetExecutingAssembly());

            if (!string.Equals("Local", currentEnvironment, StringComparison.InvariantCultureIgnoreCase))
            {
                busConfigurator.UsingAzureServiceBus((context, configurator) =>
                {
                    configurator.UseServiceBusMessageScheduler();
                    configurator.Host(configuration.GetConnectionString("ServiceBusses:WeBuyCars"));

                    configurator.UseMessageRetry(r =>
                    {
                        r.Interval(20, TimeSpan.FromSeconds(1));
                        r.Ignore(typeof(DomainException));
                    });

                    configurator.UseScheduledRedelivery(r =>
                    {
                        r.Interval(60, TimeSpan.FromMinutes(1));
                        r.Ignore(typeof(DomainException));
                    });

                    configurator.ConfigureEndpoints(context);
                });
            }
            else
            {
                busConfigurator.UsingInMemory((context, configurator) =>
                {
                    configurator.UseMessageRetry(r =>
                    {
                        r.Exponential(24, TimeSpan.FromMilliseconds(200), TimeSpan.FromHours(1),
                            TimeSpan.FromMilliseconds(200));
                        r.Ignore(typeof(DomainException));
                    });

                    configurator.ConfigureEndpoints(context);
                });
            }
        });
    }
}